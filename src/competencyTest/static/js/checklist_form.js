import { dynamicUnitDropdown, countChecklistScore, generateChecklistFormContent } from "./utils.js";

// Function to read data attributes
function getDataAttributes() {
	const container = document.getElementById('data-container');
	if (!container) return {};

	return {
	  pcas: JSON.parse(container.dataset.pcas),
	  tempStaff: JSON.parse(container.dataset.tempStaff),
	  nurses: JSON.parse(container.dataset.nurses),
	  unitsByDepartment: JSON.parse(container.dataset.unitsByDepartment),
	  passingCriteria: JSON.parse(container.dataset.passingCriteria),
	  checklistInfo: JSON.parse(container.dataset.checklistInfo),
	  checklistContent: JSON.parse(container.dataset.checklistContent),
	  departments: JSON.parse(container.dataset.departments),
	  ranks: JSON.parse(container.dataset.ranks),
	  isPcaAssessmentForm: container.dataset.isPcaAssessmentForm
	};
}
  
  // Initialize the form
function initializeForm() {

	const {
		pcas,
		tempStaff,
		nurses,
		unitsByDepartment,
		passingCriteria,
		checklistInfo,
		checklistContent,
		departments,
		ranks,
		isPcaAssessmentForm
	} = getDataAttributes();

	// Generate the form content dynamically
	generateFormContent(checklistContent, departments);

	// Call your functions with the data
	listenFormSubmit(pcas, tempStaff, nurses);
	setEntryInputs(ranks);
	resetForm();
	listenDropdowns('.select-unit', unitsByDepartment);

	if (checklistInfo.option_setting === 'one') {
		one_option_only();
	}

	if (isPcaAssessmentForm === "True") {
		pcaFormFillNames()
	}

	listenRadios(passingCriteria);
	updateTotalScore(passingCriteria);
	listenResetButtons(passingCriteria);
	listenCheckAll();
	listenResetAll();
}

// Function to generate form content dynamically
function generateFormContent(checklistContent, departments) {
	const formData = {
		content: checklistContent
	};

	const formHTML = generateChecklistFormContent(formData, departments);
	const dynamicFormContainer = document.getElementById('dynamic-form-content');

	if (dynamicFormContainer) {
		dynamicFormContainer.innerHTML = formHTML;
	}
}

function resetForm() {
	document.querySelectorAll('textarea').forEach(textarea => {
		textarea.value = ''
		const dates = document.querySelectorAll('input[type="date"]');
		dates.forEach(date => {
			date.value = new Date().toISOString().split('T')[0]
		})
	})
}
  
function toggleRadio(btn, passingCriteria) {
	const radios = btn.previousElementSibling.querySelectorAll('input');
	radios.forEach(radio => {
		radio.checked = false;
	})
	updateTotalScore(passingCriteria);
}

function listenResetButtons(passingCriteria) {
	document.querySelectorAll('button.reset-button').forEach(btn => {
		btn.addEventListener('click', () => {
			toggleRadio(btn, passingCriteria);
		})
	})
}

function checkAll(btn) {
	const col = btn.getAttribute('data-column');
	// Get the elements of class col and click them
	if (col) {
		document.querySelectorAll(`input.${col}`).forEach(element => {
			element.click();
		});
	}
}

function resetAll(btn) {
	const col = btn.getAttribute('data-column');
	// Get the elements of class col and click them
	if (col) {
		document.querySelectorAll(`button.${col}`).forEach(element => {
			element.click();
		});
	}
}

function setEntryInputs(ranks) {
	document.querySelectorAll('input.entry-input').forEach(input => {
		switch (input.getAttribute('data-input-type')) {
			case 'test_date':
				input.type = 'date';
				input.name = 'test_date_' + input.name;
				break;
			case 'date':
				input.type = 'date';
				break;
			case 'number':
				input.type = 'number';
				break;
			case 'checkbox':
				input.type = 'checkbox';
				break;
			case 'free_text':
				input.type = 'text';
				break;
			case 'rank':
				// create a select element next to the input
				const select = document.createElement('select');
				select.name = input.name;
				select.id = input.id;
				select.className = 'form-select entry-input';
				input.parentNode.insertBefore(select, input.nextSibling);
				if (input.hasAttribute('required')) {
					select.required = true;
				}

				ranks.forEach(rank => {
					const option = document.createElement('option');
					option.value = rank;
					option.textContent = rank;
					select.appendChild(option);
				})

				// Check the option "APN / NO" by default
				const defaultOption = select.querySelector('option[value="APN / NO"]');
				if (defaultOption) {
					defaultOption.selected = true;
				}

				// remove the input element
				input.remove();

				break;
			// default:
				// input.setAttribute('list', `listOptions-${input.id}`);
				// input.setAttribute('placeholder', 'Type here to search...');

				// const dataList = document.createElement('datalist');
				// dataList.id = `listOptions-${input.id}`;
				// input.required = true;
				
				// const options = JSON.parse(input.getAttribute('data-input-type'));
				// options.forEach(option => {
				// 	const opt = document.createElement('option');
				// 	opt.value = option;
				// 	opt.textContent = option;
				// 	dataList.appendChild(opt);
				// });

				// input.parentNode.appendChild(dataList);
		}
})
}


function createDepartmentSelect(input, departments) {

	const selectElement = document.createElement('select');
	selectElement.name = 'department';
	selectElement.id = 'department';
	selectElement.className = 'form-select';

	departments.forEach(dept => {
		const optionElement = document.createElement('option');
		optionElement.value = dept;
		optionElement.textContent = dept;
		selectElement.appendChild(optionElement);
	});

	const labelElement = document.createElement('label');
	labelElement.htmlFor = 'department';
	labelElement.textContent = 'Department:';

	const formGroupElement = document.createElement('div');
	formGroupElement.className = 'form-group mt-3';
	formGroupElement.appendChild(labelElement);
	formGroupElement.appendChild(selectElement);

	// append the formGroupElement to your desired parent element

}


function listenFormSubmit(pcas, temp_staff, nurses) {

	const form = document.querySelector('#complete-form')
	const submitBtn = form.querySelector('#form-submit')

	submitBtn.addEventListener('click', (e) => {

		console.log('Submit button pressed')
		
		e.preventDefault()

		if (!form.checkValidity()) {
			form.reportValidity();
			return;
		}
		
		const deptInput = form.querySelector('#department')
		const unitInput = form.querySelector('#unit')
		const nameInput = form.querySelector('#name')
		const employeenoInput = form.querySelector('#employeeno')
		
		const modalDept = document.querySelector('#modal-staff-dept')
		const modalUnit = document.querySelector('#modal-staff-unit')
		const modalName = document.querySelector('#modal-staff-name')
		const modalEmployeeNo = document.querySelector('#modal-staff-employeeno')
		const heading = document.querySelector('#found-staff')
		
		let foundOp = Object.entries(pcas).find(([key, _]) => key === employeenoInput.value);

		if (!foundOp) {
			foundOp = Object.entries(temp_staff).find(([key, _]) => key === employeenoInput.value);
		} else if (!foundOp) {
			foundOp = Object.entries(nurses).find(([key, _]) => key === employeenoInput.value);
		}

		if (foundOp) {

			console.log('Found Staff')

			const foundName = foundOp[1]['Name']
			const foundDept = foundOp[1]['Department']
			const foundUnit = foundOp[1]['Unit']

			console.log(`${foundName} ${foundDept} ${foundUnit}`)


			// Update the form inputs
			nameInput.value = foundName
			deptInput.value = foundDept
			deptInput.querySelector(`option[value="${foundDept}"]`).selected = true
			deptInput.dispatchEvent(new Event('change'))
			unitInput.value = foundUnit
			unitInput.querySelector(`option[value="${foundUnit}"]`).selected = true

			// Update the modal inputs
			modalDept.value = foundDept
			modalUnit.value = foundUnit
			modalName.value = foundName
			modalEmployeeNo.value = foundOp[0]


			heading.innerHTML = 'Update Existing Staff'
			heading.classList.add('text-success')

		} else {

			console.log('New Staff')

			// Check form validation
			if (!form.checkValidity()) {
				alert('Please fill in all required fields')
				return
			}


			// Update the modal inputs
			modalDept.value = deptInput.value
			modalUnit.value = unitInput.value
			modalName.value = nameInput.value
			modalEmployeeNo.value = employeenoInput.value

			heading.innerHTML = 'Create New Staff Profile'
			heading.classList.add('text-danger')

		}

		// Display the confirmation dialog
		const modal = new bootstrap.Modal(document.querySelector('#confirm-staff-modal'));
		modal.show();

	})

	const confirmBtn = document.querySelector('#confirm-staff-btn')
	confirmBtn.addEventListener('click', (e) => {

		// Check form validation
		if (!form.checkValidity()) {
			alert('Please fill in all required fields')
		} else {
			form.submit();
		}

	})
	
}


function one_option_only() {
	// Get the elements of class "checklist-options",
	// then add a click event listener to each element
	// if the elements have the same value of attribute "data-options",
	// when one is clicked, others will be unchecked

	const options = document.querySelectorAll('.checklist-options');
	options.forEach(option => {
		option.addEventListener('click', () => {
			const dataOptions = option.getAttribute('data-options');
			document.querySelectorAll(`.${dataOptions}`).forEach(allOption => {
				allOption.checked = false;
			});
			option.checked = true;
		});
	});
}


// Function to calculate and update the total score
function updateTotalScore(passingCriteria) {
	countChecklistScore(passingCriteria);
}
  


function listenRadios(passingCriteria) {
// Attach event listeners to all radio buttons
document.querySelectorAll('#checklist-content tbody input[type="radio"]').forEach((radio) => {
	radio.addEventListener('change', () => {
	updateTotalScore(passingCriteria);
	});
});
}
  

function listenDropdowns(elementSelection, dropdownList) {

	document.querySelectorAll(elementSelection).forEach(select => {

		const unitID = select.id
		const deptID = unitID.replace('_u','_d')
	  
		dynamicUnitDropdown(deptID,unitID,dropdownList)
	  
	})

	dynamicUnitDropdown('department','unit', dropdownList)

}

function listenCheckAll() {
	const checkAllButtons = document.querySelectorAll('button.check-all');
	checkAllButtons.forEach(btn => {
		btn.addEventListener('click', () => {
			checkAll(btn);
		});
	});
}

function listenResetAll() {
	const resetAllButtons = document.querySelectorAll('button.reset-all');
	resetAllButtons.forEach(btn => {
		btn.addEventListener('click', () => {
			resetAll(btn);
		});
	});
}


function pcaFormFillNames() {

	// Function to find input by associated label text
	const getInputByLabelText = (text) => {
		const span = document.evaluate(
			`//span[text()='${text}']`,
			document,
			null,
			XPathResult.FIRST_ORDERED_NODE_TYPE,
			null
		).singleNodeValue;

		if (!span) {
			console.log(`Label not found for ${text}`);
			return null;
		}

		// return input or select
		const closestFormGroup = span.closest('.form-group');
		if (closestFormGroup) {
			return closestFormGroup.querySelector('input, select');
		}

	};

	// Function to create N/A button for a specific field group
	const createNAButton = (labelText, fieldsToToggle) => {
		const fieldElement = getInputByLabelText(labelText);
		if (!fieldElement) return;

		const formGroup = fieldElement.closest('.form-group');
		if (!formGroup) return;

		// Check if the form group uses input-group class (Bootstrap input group)
		const inputGroup = formGroup.querySelector('.input-group');
		if (inputGroup) {
			// Create N/A button for input-group layout
			const naButton = document.createElement('button');
			naButton.className = 'btn btn-outline-secondary';
			naButton.type = 'button';
			naButton.textContent = 'N/A';

			// Add the button to the input group
			inputGroup.appendChild(naButton);

			// Add event listener for N/A button
			naButton.addEventListener('click', () => {
				fieldsToToggle.forEach((text) => {
					const element = getInputByLabelText(text);
					if (element) element.disabled = !element.disabled;
				});
			});
		} else {
			// Fallback for non-input-group layouts
			const naButton = document.createElement('button');
			naButton.className = 'btn btn-sm btn-outline-secondary ms-2';
			naButton.type = 'button';
			naButton.textContent = 'N/A';

			// Insert button right after the input/select element
			fieldElement.parentNode.insertBefore(naButton, fieldElement.nextSibling);

			// Add event listener for N/A button
			naButton.addEventListener('click', () => {
				fieldsToToggle.forEach((text) => {
					const element = getInputByLabelText(text);
					if (element) element.disabled = !element.disabled;
				});
			});
		}
	};

	// Create N/A button for instructor fields
	createNAButton('指導員姓名', [
		'指導員姓名',
		'指導員職級',
		'指導講解日期'
	]);

	// Create N/A button for observer fields
	createNAButton('觀察員姓名', [
		'觀察員姓名',
		'觀察員職級',
		'觀察下實習日期'
	]);

	// Find the instructor group for the Apply to ALL button
	const instructorGroup = document.evaluate(
		"//span[text()='指導員姓名']/ancestor::div[contains(@class, 'form-group')][1]",
		document,
		null,
		XPathResult.FIRST_ORDERED_NODE_TYPE,
		null
	).singleNodeValue;

	if (!instructorGroup) return;

	// Create Apply to ALL button container
	const applyButtonContainer = document.createElement('div');
	applyButtonContainer.className = 'd-flex justify-content-end gap-2 mt-2';

	const applyButton = document.createElement('button');
	applyButton.className = 'btn btn-sm btn-outline-primary';
	applyButton.type = 'button';
	applyButton.textContent = 'Apply to ALL';

	applyButtonContainer.appendChild(applyButton);

	// Add Apply to ALL button before the instructor group
	instructorGroup.parentNode.insertBefore(applyButtonContainer, instructorGroup);

	// Apply to ALL Button handler
	applyButton.addEventListener('click', () => {

		// Source elements
		const sourceName = getInputByLabelText('指導員姓名');
		const sourceRank = getInputByLabelText('指導員職級'); // First 職級 (指導員)
		const sourceDate = getInputByLabelText('指導講解日期');

		console.log(sourceName.name)
		console.log(sourceRank.name)
		console.log(sourceDate.name)

		// Copy to 觀察員 section
		getInputByLabelText('觀察員姓名').value = sourceName.value;
		getInputByLabelText('觀察員職級').value = sourceRank.value;
		getInputByLabelText('觀察下實習日期').value = sourceDate.value;

		// Copy to 評核員 section
		getInputByLabelText('評核員姓名').value = sourceName.value;
		getInputByLabelText('職級').value = sourceRank.value;

		getInputByLabelText('日期').value = sourceDate.value;
	});
}


// Call the initialize function when the DOM is loaded
document.addEventListener('DOMContentLoaded', initializeForm);


