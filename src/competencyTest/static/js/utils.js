export function dynamicUnitDropdown(ele_d, ele_u, unitsByDepartment) {

  // console.log(unitsByDepartment)
  const departmentSelect = document.getElementById(ele_d);

  departmentSelect.addEventListener('change', () => {
    const optionDepartment = departmentSelect.value;
    updateUnitDropdown(ele_u, unitsByDepartment, optionDepartment)
  });
  
  // Trigger the change event to populate the unit dropdown on page load
  departmentSelect.dispatchEvent(new Event('change'));
  
}

function updateUnitDropdown(ele_u, unitsByDepartment, optionDepartment) {

  // console.log(`Selected Department: ${optionDepartment}`);
  const unitSelect = document.getElementById(ele_u);
  unitSelect.innerHTML = '';

  if (optionDepartment === 'ALL') {
    const option = document.createElement('option');
    option.value = "ALL";
    option.text = "ALL";
    unitSelect.appendChild(option);

    Object.values(unitsByDepartment).forEach(units => {
      units.sort();
      for (const u of units) {
        if (u === 'ALL') continue;
        const option = document.createElement('option');
        option.value = u;
        option.text = u;
        unitSelect.appendChild(option);
      }
    })

  } else {

    const units = unitsByDepartment[optionDepartment] || [];

    // console.log(units);
    
    units.sort((a, b) => {
      if (a === "ALL") return -1; // Move 'ALL' to the beginning
      if (b === "ALL") return 1; // Move 'ALL' to the beginning
      return 0; // Keep the original order for other items
    });
    for (const u of units) {
      const option = document.createElement('option');
      option.value = u;
      option.text = u;
      unitSelect.appendChild(option);
    }

  }
}


export function alertMsgModal(
  msg,
  headerTitle="Alert",
  headerColor="danger",
  footerButtonText="Close",
  footerButtonColor="secondary"
) {
  // get the modal element by id
  const modal = document.getElementById('general-alert-modal');
  const modalHeader = document.getElementById('general-alert-modal-header');
  const modalHeaderTitle = document.getElementById('general-alert-modal-header-title');
  const modalMsg = document.getElementById('general-alert-modal-msg');
  const modalFooterButton = document.getElementById('general-alert-modal-footer-button');

  // set the modal content
  modalHeaderTitle.innerText = headerTitle;
  modalHeader.className = `modal-header text-bg-${headerColor}`;
  modalHeaderTitle.innterText = headerTitle;
  modalMsg.innerText = msg;
  modalFooterButton.innerText = footerButtonText;
  modalFooterButton.className = `btn btn-${footerButtonColor}`;

  // show the modal
  const bootstrapModal = new bootstrap.Modal(modal);
  bootstrapModal.show();
}

/**
 * Rearranges form keys in the desired order
 * @param {Object} obj - The form content object
 * @returns {Object} - Rearranged object with keys in proper order
 */
export function rearrangeFormKeys(obj) {
  // Define the desired order of keys
  const order = ['basic_entry', 'intro', 'questions', 'additional_entry'];

  // Create a new object to hold the rearranged keys
  const rearranged = {};

  // Loop through the desired order and add the keys from the original object
  order.forEach(key => {
      if (key in obj) {
          rearranged[key] = obj[key];
      }
  });

  return rearranged;
}

/**
 * Generates HTML content for a multiple choice (MC) form modal
 * @param {Object} data - The form data containing info and content
 * @returns {string} - Generated HTML content for the modal
 */
export function generateMCModalContent(data) {
  // Initialize content variable
  let modalContent = '<form id="form-reply">';

  // Loop through the parts in the form data
  for (const [part, page] of Object.entries(rearrangeFormKeys(data.content))) {

    modalContent += `
      <div class="card mt-5">
        <div class="card-header">
          <h3>${page.header}</h3>
        </div>
        <div class="card-body">
          <div class="form-group">`;

    if (part === 'intro') {
      modalContent += `
        <label
          class="mt-3 mb-3"
        >${page.details}</label>`;
    } else if (part === 'questions') {

      for (const [id, item] of Object.entries(page.details)) {

        modalContent += `
              <label
                      class="question-text mt-3 mb-3"
                      for="${id}"
                    ><strong>${parseInt(id.replace('q', ''))} ) <br/>${item.q}</strong></label>`;

        if (item.opt.length > 0) {
          modalContent += `
            <div class="form-check" id="div-${id}"></div>`;
        } else {
          modalContent += `
            <input
              style="display: none"
              type="text"
              id="div-${id}"
              class="form-control"
              disabled
            />`;
        }

        // if this iteration is not the last one, add the horizontal line
        if (id !== Object.keys(page.details)[Object.keys(page.details).length - 1]) {
          modalContent += `
            <hr />`;
        }

      }

    }

    modalContent += `
          </div>
        </div>
      </div>`;
  }

  // Close the form tag
  modalContent += `</form>`;

  return modalContent;
}

/**
 * Generates HTML content for a checklist form modal
 * @param {Object} data - The form data containing info and content
 * @returns {string} - Generated HTML content for the modal
 */
export function generateChecklistModalContent(data) {
  // Initialize content variable
  let modalContent = '<form id="form-reply">';

  // Loop through the parts in the form data
  for (const [part, page] of Object.entries(rearrangeFormKeys(data.content))) {

    modalContent += `
      <div class="card mt-5">
        <div class="card-header">
          <h3>${page.header}</h3>
        </div>
        <div class="card-body">
          <div class="form-group">`;

    if (part === 'intro') {
      modalContent += `<label class="mt-3 mb-3">${page.details}</label>`;
    } else if (part === 'questions') {

      modalContent += `
        <div class="table-responsive">
          <table class="table table-striped table-bordered table-hover" id="checklist-table">
            <thead>
              <tr>
                <th></th>
                <th class="h-center v-center bold-large">${page.itemcol}</th>`;

      for (const [, opt] of Object.entries(page.details.options)) {
        modalContent += `
          <th class="h-center v-center bold-large">
            <div ${opt.count_score ? 'data-cs="true"' : 'data-cs="false"'}>
              ${opt.name}
            </div>
          </th>`;
      }

      modalContent += `</tr></thead><tbody>`;

      // Generate table rows for each question item
      modalContent += generateChecklistTableRows(page.details);

      // Add total score section if any option has count_score = true
      const hasCountScore = Object.values(page.details.options).some(opt => opt.count_score === true);
      if (hasCountScore) {
        modalContent += `
        <tr>
          <td></td>
          <td class="text-end fw-bold">
            <span class="fw-bold">TOTAL SCORE: </span>
            <span class="fw-bold" id="total-score">--</span>
          </td>
        </tr>`;
      }

      modalContent += `</tbody></table></div>`;

    } else {
      // Other parts (basic_entry, additional_entry)
      modalContent += generateFormEntryFields(page.details);
    }

    modalContent += `
    </div>
    </div>
    </div>`;

  }

  // Close the form tag
  modalContent += '</form>';

  return modalContent;
}

/**
 * Helper function to format question ID for display
 * @param {string} id - The question ID to format
 * @returns {string} - Formatted question ID
 */
function formatQuestionId(id) {
  return id.replace(/^q0?/, '').replace(/_/g, '.');
}

/**
 * Generates table rows for checklist questions
 * @param {Object} details - The question details object
 * @returns {string} - Generated HTML for table rows
 */
function generateChecklistTableRows(details) {
  let rowsContent = '';

  for (const [id, item] of Object.entries(details.items)) {
    rowsContent += `<tr>`;

    // Handle different subtitle types
    if (item.subtitle === 1) {
      // Subtitle type 1 - main section header
      if (item.essential) {
        rowsContent += `
          <td style="color: red; font-weight: bold" data-essential="true">
            <p>${formatQuestionId(id)}*</p>
          </td>`;
      } else {
        rowsContent += `
          <td>
            <p>${formatQuestionId(id)}</p>
          </td>`;
      }
      rowsContent += `
        <td colspan="${Object.keys(details.options).length + 1}">
          <p>${item.text}</p>
        </td>`;

    } else if (item.subtitle === 2) {
      // Subtitle type 2 - subsection header (italic)
      if (item.essential) {
        rowsContent += `
          <td style="color: red; font-weight: bold;" data-essential="true">
            <p><i>${formatQuestionId(id)}*</i></p>
          </td>`;
      } else {
        rowsContent += `
          <td>
            <p><i>${formatQuestionId(id)}</i></p>
          </td>`;
      }
      rowsContent += `
        <td colspan="${Object.keys(details.options).length + 1}">
          <i>${item.text}</i>
        </td>`;

    } else {
      // Regular questions (subtitle 0, 3, or undefined)
      if (item.subtitle === 3) {
        // Subtitle type 3 - special layout with container-fluid
        rowsContent += `
          <td></td>
          <td class="p-0">
            <div class="container-fluid p-0">
              <div class="row g-0">
                <div class="col-3 border-end py-2 px-1">`;
        if (item.essential) {
          rowsContent += `<span style="color: red; font-weight: bold" data-essential="true">${formatQuestionId(id)}*</span>`;
        } else {
          rowsContent += `<span>${formatQuestionId(id)}</span>`;
        }
        rowsContent += `
                </div>
                <div class="col-9 py-2 px-1 item-content" data-essential="false">${item.text}</div>
              </div>
            </div>
          </td>`;
      } else {
        // Regular layout
        if (item.essential) {
          rowsContent += `<td style="color: red; font-weight: bold" data-essential="true">${formatQuestionId(id)}*</td>`;
        } else {
          rowsContent += `<td>${formatQuestionId(id)}</td>`;
        }
        rowsContent += `<td class="item-content" data-essential="false">${item.text}</td>`;
      }

      // Add option columns for regular questions
      for (const [k, opt] of Object.entries(details.options)) {
        if (opt.type === 'checkbox') {
          rowsContent += `
            <td>
              <div>
                <input
                  type="radio"
                  class="btn-check checklist-options checkbox-cells ${id}_options col-${k}"
                  data-options="${id}_options"
                  id="${id}_${k}_tick"
                  value="true"
                  autocomplete="off"
                  name="${id}__${k}"`;

          // add data-score="true" if opt.count_score is true
          if (opt.count_score) {
            rowsContent += ` data-score="true"`;
          } else {
            rowsContent += ` data-score="false"`;
          }

          rowsContent += `
                />
                <label
                  class="btn btn-outline-success"
                  for="${id}_${k}_tick"
                ><i class="fa-solid fa-check"></i></label>
              </div>
            </td>`;
        } else {
          rowsContent += `
            <td class="h-center">
              <textarea
                class="form-control textarea-cells"
                name="${id}__${k}"
                id="${id}_${k}"
              ></textarea>
            </td>`;
        }
      }
    }
    rowsContent += `</tr>`;
  }

  return rowsContent;
}

/**
 * Generates form entry fields for basic_entry and additional_entry sections
 * @param {Object} details - The entry details object
 * @returns {string} - Generated HTML for form entry fields
 */
function generateFormEntryFields(details) {
  let fieldsContent = '';

  for (const [k, item] of Object.entries(details)) {
    if (item.input === 'department_and_unit') {
      fieldsContent += `
      <div class="form-group input-group mt-3 mb-3">
      <span class="input-group-text">${item.title}</span>
      <div class="form-group mx-2">
      <textarea class="form-control textarea-cells" name="${k}_d" id="${k}_d"></textarea>
      </div>
      <div class="form-group">
      <textarea class="form-control textarea-cells" id="${k}_u"></textarea>
      </div>
      </div>`;
    } else {
      fieldsContent += `
      <div class="form-group input-group mt-3 mb-3">
      <span class="input-group-text">${item.title}</span>
      <textarea class="form-control textarea-cells" id="${k}" data-input-type="${item.input}"></textarea>
      </div>`;
    }
  }

  return fieldsContent;
}

/**
 * Fills checklist form with existing data
 * @param {HTMLElement} modalBody - The modal body element containing the form
 * @param {Object} formContentObj - The form data to fill in
 */
export function fillChecklistForm(modalBody, formContentObj) {
  const checkboxCells = modalBody.querySelectorAll('input.checkbox-cells');
  const textareaCells = modalBody.querySelectorAll('textarea.textarea-cells');

  checkboxCells.forEach(cell => {
    cell.checked = false;

    // Handle the new naming convention with double underscores
    // Extract question ID and option ID from the name attribute (e.g., "q1__o1")
    const nameAttr = cell.getAttribute('name');
    if (nameAttr && nameAttr.includes('__')) {
      const [q, o] = nameAttr.split('__');
      // Check if this checkbox should be checked (only for 'true' values since we only have tick checkboxes)
      if (formContentObj[q] && formContentObj[q][o] === 'true') {
        cell.checked = true;
      }
    } else {
      // Fallback to old logic for backward compatibility
      const [q,o] = cell.id.replace('_tick', '').replace('_cross', '').split('_');
      if (formContentObj[q] && formContentObj[q][o] === 'true' && cell.id.endsWith('_tick')) {
        cell.checked = true;
      } else if (formContentObj[q] && formContentObj[q][o] === 'false' && cell.id.endsWith('_cross')) {
        cell.checked = true;
      }
    }
    cell.disabled = true;
  });

  textareaCells.forEach(cell => {
    cell.value = '';

    // Check if cell has a name attribute with double underscores (new format)
    const nameAttr = cell.getAttribute('name');
    if (nameAttr && nameAttr.includes('__')) {
      const [q, o] = nameAttr.split('__');
      if (formContentObj[q] && formContentObj[q][o]) {
        cell.value = formContentObj[q][o];
      }
    } else if (cell.id.includes('_entry')) {
      // Handle entry fields
      if (formContentObj[cell.id]) {
        cell.value = formContentObj[cell.id];
      }
    } else {
      // Fallback to old logic for backward compatibility
      const [q,o] = cell.id.split('_');
      if (formContentObj[q] && formContentObj[q][o]) {
        cell.value = formContentObj[q][o];
      }
    }
    cell.disabled = true;
  });
}

/**
 * Generates HTML content for a full checklist form (not modal)
 * @param {Object} data - The form data containing info and content
 * @param {Array} departments - List of departments for dropdowns
 * @returns {string} - Generated HTML content for the form
 */
export function generateChecklistFormContent(data, departments) {
  // Initialize content variable
  let formContent = '';

  // Loop through the parts in the form data
  for (const [part, page] of Object.entries(rearrangeFormKeys(data.content))) {
    if (!page.details) continue;

    formContent += `
      <div class="card mt-5">
        <div class="card-header">
          <h3>${page.header}</h3>
        </div>
        <div class="card-body">
          <div class="form-group">`;

    if (part === 'intro') {
      formContent += `
        <label class="mt-3 mb-3">${page.details}</label>`;
    } else if (part === 'questions') {
      formContent += generateChecklistTableForForm(page);
    } else {
      // Other parts (basic_entry, additional_entry)
      formContent += generateFormEntryFieldsForForm(page.details, departments);
    }

    formContent += `
          </div>
        </div>
      </div>`;
  }

  return formContent;
}

/**
 * Generates checklist table for the main form (with interactive elements)
 * @param {Object} page - The page object containing table data
 * @returns {string} - Generated HTML for the checklist table
 */
function generateChecklistTableForForm(page) {
  let tableContent = `
    <div class="table-responsive">
      <table class="table table-striped table-bordered table-hover" id="checklist-content">
        <thead>
          <tr>
            <th></th>
            <th class="h-center v-center bold-large">${page.itemcol}</th>`;

  // Generate header columns for options
  for (const [k, opt] of Object.entries(page.details.options)) {
    if (opt.type === 'checkbox') {
      tableContent += `
        <th>
          <div
            class="d-flex flex-column align-items-center bold-large"
            ${opt.count_score ? 'data-cs="true"' : 'data-cs="false"'}
          >
            ${opt.name}
            <button
              type="button"
              class="btn btn-success mt-2 check-all"
              data-column="col-${k}"
            >Check ALL</button>
            <button
              type="button"
              class="btn btn-primary mt-2 reset-all"
              data-column="col-${k}"
            >Reset ALL</button>
          </div>
        </th>`;
    } else {
      tableContent += `<th class="h-center v-center bold-large">${opt.name}</th>`;
    }
  }

  tableContent += `</tr></thead><tbody>`;

  // Generate table rows for each question item
  tableContent += generateChecklistTableRowsForForm(page.details);

  // Add total score section if any option has count_score = true
  const hasCountScore = Object.values(page.details.options).some(opt => opt.count_score === true);
  if (hasCountScore) {
    tableContent += `
    <tr>
      <td></td>
      <td>
        <span class="bold-large">TOTAL SCORE: </span>
        <span class="bold-large" id="total-score">--</span>
        <input type="hidden" name="total-score" id="total-score-input" value="0">
      </td>
      <td>
        <span class="bold-large" id="pass-fail">--</span>
        <input type="hidden" name="pass-fail" id="pass-fail-input" value="fail">
      </td>
    </tr>`;
  }

  tableContent += `</tbody></table></div>`;

  return tableContent;
}

/**
 * Generates table rows for checklist questions in the main form
 * @param {Object} details - The question details object
 * @returns {string} - Generated HTML for table rows
 */
function generateChecklistTableRowsForForm(details) {
  let rowsContent = '';

  for (const [id, item] of Object.entries(details.items)) {
    rowsContent += `<tr>`;

    // Handle different subtitle types
    if (item.subtitle === 1) {
      // Subtitle type 1 - main section header
      if (item.essential) {
        rowsContent += `
          <td style="color: red; font-weight: bold" data-essential="true">
            <p>${formatQuestionId(id)}*</p>
          </td>`;
      } else {
        rowsContent += `
          <td>
            <p>${formatQuestionId(id)}</p>
          </td>`;
      }
      rowsContent += `
        <td colspan="${Object.keys(details.options).length + 1}">
          <p>${item.text}</p>
        </td>`;

    } else if (item.subtitle === 2) {
      // Subtitle type 2 - subsection header (italic)
      if (item.essential) {
        rowsContent += `
          <td style="color: red; font-weight: bold;" data-essential="true">
            <p><i>${formatQuestionId(id)}*</i></p>
          </td>`;
      } else {
        rowsContent += `
          <td>
            <p><i>${formatQuestionId(id)}</i></p>
          </td>`;
      }
      rowsContent += `
        <td colspan="${Object.keys(details.options).length + 1}">
          <i>${item.text}</i>
        </td>`;

    } else {
      // Regular questions (subtitle 0, 3, or undefined)
      if (item.subtitle === 3) {
        // Subtitle type 3 - special layout with container-fluid
        rowsContent += `
          <td></td>
          <td class="p-0">
            <div class="container-fluid p-0">
              <div class="row g-0">
                <div class="col-3 border-end py-2 px-1">`;
        if (item.essential) {
          rowsContent += `<span style="color: red; font-weight: bold" data-essential="true">${formatQuestionId(id)}*</span>`;
        } else {
          rowsContent += `<span>${formatQuestionId(id)}</span>`;
        }
        rowsContent += `
                </div>
                <div class="col-9 py-2 px-1 item-content" data-essential="false">${item.text}</div>
              </div>
            </div>
          </td>`;
      } else {
        // Regular layout
        if (item.essential) {
          rowsContent += `<td style="color: red; font-weight: bold" data-essential="true">${formatQuestionId(id)}*</td>`;
        } else {
          rowsContent += `<td>${formatQuestionId(id)}</td>`;
        }
        rowsContent += `<td class="item-content" data-essential="false">${item.text}</td>`;
      }

      // Add option columns for regular questions
      for (const [k, opt] of Object.entries(details.options)) {
        if (opt.type === 'checkbox') {
          rowsContent += `
            <td>
              <div>
                <input
                  type="radio"
                  class="btn-check checklist-options ${id}_options col-${k}"
                  data-options="${id}_options"
                  id="${id}_${k}_tick"
                  value="true"
                  autocomplete="off"
                  name="${id}__${k}"`;

          // add data-score="true" if opt.count_score is true
          if (opt.count_score) {
            rowsContent += ` data-score="true"`;
          } else {
            rowsContent += ` data-score="false"`;
          }

          rowsContent += `
                />
                <label
                  class="btn btn-outline-success"
                  for="${id}_${k}_tick"
                ><i class="fa-solid fa-check"></i></label>
              </div>

              <button
                type="button"
                class="btn btn-outline-primary mt-2 col-${k} reset-button"
                value="reset"
              >Reset</button>
            </td>`;
        } else {
          rowsContent += `
            <td class="h-center">
              <textarea
                class="form-control"
                name="${id}__${k}">
              </textarea>
            </td>`;
        }
      }
    }
    rowsContent += `</tr>`;
  }

  return rowsContent;
}

/**
 * Generates form entry fields for basic_entry and additional_entry sections in the main form
 * @param {Object} details - The entry details object
 * @param {Array} departments - List of departments for dropdowns
 * @returns {string} - Generated HTML for form entry fields
 */
function generateFormEntryFieldsForForm(details, departments) {
  let fieldsContent = '';

  for (const [k, item] of Object.entries(details)) {
    if (item.input === 'department_and_unit') {
      fieldsContent += `
      <div class="form-group input-group mt-3 mb-3">
        <span class="input-group-text">${item.title}</span>
        <div class="form-group mx-2">
          <select class="form-control form-select select-dept" name="${k}_d" id="${k}_d">`;

      departments.forEach(dept => {
        fieldsContent += `<option value="${dept}">${dept}</option>`;
      });

      fieldsContent += `
          </select>
        </div>
        <div class="form-group">
          <select class="form-control form-select select-unit" name="${k}_u" id="${k}_u">
            <!-- generated by javascript -->
          </select>
        </div>
      </div>`;
    } else if (typeof item.input !== 'string') {
      // Handle select dropdowns
      fieldsContent += `
      <span>${item.title}:</span>
      <select class="form-control form-select" name="${k}">
        <option value="">-- Please Select --</option>`;

      item.input.forEach(opt => {
        fieldsContent += `<option value="${opt}">${opt}</option>`;
      });

      fieldsContent += `</select>`;
    } else {
      fieldsContent += `
      <div class="form-group input-group mt-3 mb-3">
        <span class="input-group-text">${item.title}</span>
        <input
          class="form-control entry-input"
          name="${k}"
          data-input-type="${item.input}"
          ${item.required ? 'required' : ''}
        />
      </div>`;
    }
  }

  return fieldsContent;
}

/**
 * Counts and updates the total score for checklist forms
 * @param {Object} passingCriteria - Optional passing criteria for pass/fail determination
 * @param {string} tableSelector - Optional table selector (defaults to auto-detect)
 */
export function countChecklistScore(passingCriteria = null, tableSelector = null) {
  console.log('Counting Score');

  // Auto-detect table selector if not provided
  if (!tableSelector) {
    // Try main form table first, then modal table
    if (document.querySelector('#checklist-content')) {
      tableSelector = '#checklist-content';
      console.log('Using main form table: #checklist-content');
    } else if (document.querySelector('#checklist-table')) {
      tableSelector = '#checklist-table';
      console.log('Using modal table: #checklist-table');
    } else {
      console.warn('No checklist table found');
      return;
    }
  } else {
    console.log('Using provided table selector:', tableSelector);
  }

  // Initialize total score and full score
  let totalScore = 0;
  let fullScore = 0;

  // Track if all essential questions are checked
  let allEssentialsChecked = true;

  // Count N/A (不適用) selections to subtract from total possible score
  let naCount = 0;

  // First, identify which columns contain "不適用" options and count them
  const naColumns = [];
  document.querySelectorAll(`${tableSelector} thead th`).forEach((th, index) => {
    const div = th.querySelector('div');
    if (div && div.textContent.includes('不適用')) {
      console.log(`Found N/A column at index ${index}`);
      naColumns.push(index);
    }
  });

  // Count how many rows have "不適用" checked
  if (naColumns.length > 0) {
    document.querySelectorAll(`${tableSelector} tbody tr`).forEach(row => {
      // Check if any "不適用" option is checked in this row
      const hasNAChecked = naColumns.some(columnIndex => {
        const naInput = row.querySelector(`td:nth-child(${columnIndex + 1}) input[type="radio"]:checked`);
        return naInput !== null;
      });

      if (hasNAChecked) {
        naCount++;
      }
    });
  }

  // Loop through all <th> elements in the table header
  document.querySelectorAll(`${tableSelector} thead th`).forEach((th, index) => {
    // Find the <div> inside the <th> and check if it has data-cs="true"
    const div = th.querySelector('div');
    if (div && div.getAttribute('data-cs') === 'true') {

      // Get the column index
      const columnIndex = index;

      // Count the number of checked radio buttons with data-score="true" in this column
      const checkedCount = document.querySelectorAll(
        `${tableSelector} tbody td:nth-child(${columnIndex + 1}) input[type="radio"][data-score="true"]:checked`
      ).length;

      // Add to the total score
      totalScore += checkedCount;

      // Calculate the full score (total number of questions with data-score="true" in this column)
      const fullCount = document.querySelectorAll(
        `${tableSelector} tbody td:nth-child(${columnIndex + 1}) input[type="radio"][data-score="true"]`
      ).length;

      // Subtract N/A count from full score for this column
      fullScore += (fullCount - naCount);

      // Check essential questions if required
      if (passingCriteria && passingCriteria.essential) {
        document.querySelectorAll(
          `${tableSelector} tbody td:nth-child(${columnIndex + 1}) input[type="radio"][data-score="true"]`
        ).forEach((radio) => {
          // Find the row of the radio button,
          // then check if the first <td> has data-essential="true"
          // If so, check if the radio button is checked
          // If not, set allEssentialsChecked to false
          const row = radio.closest('tr');
          const essentialCell = row.querySelector('td[data-essential="true"]');

          // Skip essential check if this row has N/A checked
          const hasNAChecked = naColumns.some(naColumnIndex => {
            const naInput = row.querySelector(`td:nth-child(${naColumnIndex + 1}) input[type="radio"]:checked`);
            return naInput !== null;
          });

          if (essentialCell && !radio.checked && !hasNAChecked) {
            allEssentialsChecked = false;
          }
        });
      }
    }
  });

  // Update the "TOTAL SCORE" display
  console.log(`Score calculation: ${totalScore} / ${fullScore} (N/A count: ${naCount})`);
  const totalScoreElement = document.getElementById('total-score');
  const totalScoreInput = document.getElementById('total-score-input');
  if (totalScoreElement) {
    totalScoreElement.textContent = `${totalScore} / ${fullScore}`;
    if (totalScoreInput) {
      totalScoreInput.value = totalScore;
    }
  } else {
    console.warn('total-score element not found');
  }

  // Calculate the percentage score and determine PASS/FAIL if passing criteria provided
  if (passingCriteria) {
    const percentageScore = fullScore === 0 ? 0 : (totalScore / fullScore) * 100;

    const passFailElement = document.getElementById('pass-fail');
    const passFailInput = document.getElementById('pass-fail-input');
    if (passFailElement) {
      if (passingCriteria.essential && !allEssentialsChecked) {
        // Fail if essential questions are not all checked
        passFailElement.textContent = 'FAIL';
        passFailElement.style.color = 'red';
        if (passFailInput) passFailInput.value = 'fail';
      } else if (percentageScore >= passingCriteria.passing_pct) {
        // Pass if percentage score meets the criteria
        passFailElement.textContent = 'PASS';
        passFailElement.style.color = 'green';
        if (passFailInput) passFailInput.value = 'pass';
      } else {
        // Fail otherwise
        passFailElement.textContent = 'FAIL';
        passFailElement.style.color = 'red';
        if (passFailInput) passFailInput.value = 'fail';
      }
    }
  }
}
