// Edit Test Modal Functions
function editTestType__onChange() {
  const selectedType = $('#edit-test-type').val();
  const testsOverview = window.testsOverview; // Will be set from template
  
  // Clear and reset dependent dropdowns
  $('#edit-test-name').empty().append('<option value="">-- Choose Test --</option>');
  $('#edit-test-version').empty().append('<option value="">-- Choose Version --</option>');
  $('#edit-test-name').prop('disabled', true);
  $('#edit-test-version').prop('disabled', true);
  $('#edit-test-confirm').prop('disabled', true);
  
  if (selectedType) {
    // Filter tests by type
    const filteredTests = Object.entries(testsOverview).filter(([testName, versions]) => {
      const latestVersion = Object.values(versions)[0];
      return latestVersion && latestVersion.form === selectedType;
    });
    
    if (filteredTests.length > 0) {
      $('#edit-test-name').prop('disabled', false);
      filteredTests.forEach(([testName, versions]) => {
        const latestVersion = Object.values(versions)[0];
        const displayName = testName.replace(/_/g, ' ');
        const title = latestVersion.title ? latestVersion.title.substring(0, 30) + '...' : '';
        const option = $('<option>')
          .val(testName)
          .text(`${displayName} | ${title}`);
        $('#edit-test-name').append(option);
      });
    }
  }
}

function editTestName__onChange() {
  const selectedTest = $('#edit-test-name').val();
  const testsOverview = window.testsOverview;
  
  // Clear and reset version dropdown
  $('#edit-test-version').empty().append('<option value="">-- Choose Version --</option>');
  $('#edit-test-version').prop('disabled', true);
  $('#edit-test-confirm').prop('disabled', true);
  
  if (selectedTest && testsOverview[selectedTest]) {
    $('#edit-test-version').prop('disabled', false);
    
    // Add versions for selected test
    Object.entries(testsOverview[selectedTest]).forEach(([version, info]) => {
      const option = $('<option>')
        .val(version)
        .text(`${info.version} - ${info.version_name}`);
      $('#edit-test-version').append(option);
    });
  }
}

function editTestVersion__onChange() {
  const selectedVersion = $('#edit-test-version').val();
  $('#edit-test-confirm').prop('disabled', !selectedVersion);
}

function editTestConfirm() {
  const testType = $('#edit-test-type').val();
  const testName = $('#edit-test-name').val();
  const testVersion = $('#edit-test-version').val();
  
  if (testType && testName && testVersion) {
    // Close the selection modal
    $('#editTestModal').modal('hide');
    
    // Fetch test data and show edit form
    fetchTestDataAndShowEditForm(testType, testName, testVersion);
  }
}

function fetchTestDataAndShowEditForm(testType, testName, testVersion) {
  // Show loading in the full modal
  $('#editTestFullModalLabel').text(`Edit ${testName.replace(/_/g, ' ')} (${testVersion})`);
  $('#edit-test-content').html('<div class="text-center"><div class="spinner-border" role="status"><span class="visually-hidden">Loading...</span></div></div>');
  $('#editTestFullModal').modal('show');
  
  // Fetch test data via AJAX
  $.ajax({
    url: '/api/get-test-data',
    method: 'GET',
    data: {
      test_name: testName,
      version: testVersion,
      test_type: testType
    },
    success: function(data) {
      if (testType === 'checklist') {
        generateChecklistEditForm(data);
      } else if (testType === 'mc') {
        generateMCEditForm(data);
      }
    },
    error: function() {
      $('#edit-test-content').html('<div class="alert alert-danger">Error loading test data. Please try again.</div>');
    }
  });
}

function generateChecklistEditForm(data) {
  const content = data.content;
  const info = data.info;
  
  let html = `
    <form id="edit-checklist-form">
      <input type="hidden" name="test_name" value="${info.test_name}" />
      <input type="hidden" name="test_version" value="${info.version}" />
      
      <div class="card mt-3">
        <div class="card-header">
          <h3>Test Configuration</h3>
        </div>
        <div class="card-body">
          <div class="mb-3">
            <label class="form-label">Version:</label>
            <input class="form-control" type="text" value="${info.version}" disabled />
          </div>
          <div class="mb-3">
            <label class="form-label">Version Name:</label>
            <input class="form-control" type="text" name="version-name" value="${info.version_name || ''}" />
          </div>
          <div class="mb-3">
            <label class="form-label">Passing Criteria:</label>
            <input class="form-control" type="text" name="passing-criteria" value="${info.passing_criteria || ''}" />
          </div>
        </div>
      </div>
      
      <div class="card mt-3">
        <div class="card-header">
          <input class="form-control" type="text" name="intro-header" value="${content.intro?.header || ''}" placeholder="Test Name Here" />
        </div>
        <div class="card-body">
          <textarea class="form-control" name="intro-details" rows="5" placeholder="Introduction / Instruction Here">${content.intro?.details || ''}</textarea>
        </div>
      </div>
      
      <div class="card mt-3">
        <div class="card-header">
          <input class="form-control" type="text" name="basic-entry-header" value="${content.basic_entry?.header || ''}" placeholder="Basic Entry Header" />
        </div>
        <div class="card-body">
          <div id="basic-entry-fields">`;
  
  // Add existing basic entry fields
  if (content.basic_entry?.details) {
    Object.entries(content.basic_entry.details).forEach(([key, field]) => {
      html += `
            <div class="mb-3 entry-field" data-entry-key="${key}">
              <div class="input-group">
                <span class="input-group-text">Title:</span>
                <input class="form-control" type="text" name="basic-entry-title-${key}" value="${field.title || ''}" placeholder="Field Title" />
                <span class="input-group-text">Type:</span>
                <select class="form-control form-select" name="basic-entry-type-${key}">
                  <option value="text" ${field.input === 'text' ? 'selected' : ''}>Text</option>
                  <option value="date" ${field.input === 'date' ? 'selected' : ''}>Date</option>
                  <option value="test_date" ${field.input === 'test_date' ? 'selected' : ''}>Test Date</option>
                  <option value="rank" ${field.input === 'rank' ? 'selected' : ''}>Rank</option>
                  <option value="department_and_unit" ${field.input === 'department_and_unit' ? 'selected' : ''}>Department & Unit</option>
                  <option value="custom_options" ${Array.isArray(field.input) ? 'selected' : ''}>Custom Options</option>
                </select>
                <button type="button" class="btn btn-danger ms-2" onclick="removeEntryField(this)"><i class="fas fa-trash"></i></button>
              </div>
              ${Array.isArray(field.input) ? `
              <div class="mt-2">
                <input class="form-control" type="text" name="basic-entry-options-${key}" value="${field.input.join('|')}" placeholder="Options separated by |" />
              </div>` : ''}
              <div class="form-check mt-2">
                <input class="form-check-input" type="checkbox" name="basic-entry-required-${key}" ${field.required ? 'checked' : ''} />
                <label class="form-check-label">Required</label>
              </div>
            </div>`;
    });
  }
  
  html += `
          </div>
          <button type="button" class="btn btn-success" onclick="addEntryField('basic')">Add Basic Entry Field</button>
        </div>
      </div>
      
      <div class="card mt-3">
        <div class="card-header">
          <input class="form-control" type="text" name="questions-header" value="${content.questions?.header || ''}" placeholder="Checklist Header" />
        </div>
        <div class="card-body">
          <div class="mb-3">
            <label class="form-label">Item Column Name:</label>
            <input class="form-control" type="text" name="col-item" value="${content.questions?.itemcol || ''}" placeholder="Column Name (e.g. Item / Steps)" />
          </div>
          
          <div class="mb-3">
            <h5>Column Configuration</h5>
            <div id="column-config">`;
  
  // Add existing column configurations
  if (content.questions?.details?.options) {
    Object.entries(content.questions.details.options).forEach(([key, option]) => {
      html += `
              <div class="mb-3 column-field" data-column-key="${key}">
                <div class="input-group">
                  <span class="input-group-text">Column Name:</span>
                  <input class="form-control" type="text" name="column-name-${key}" value="${option.name || ''}" placeholder="Column Name" />
                  <span class="input-group-text">Type:</span>
                  <select class="form-control form-select" name="column-type-${key}">
                    <option value="checkbox" ${option.type === 'checkbox' ? 'selected' : ''}>Checkbox</option>
                    <option value="text" ${option.type === 'text' ? 'selected' : ''}>Text</option>
                  </select>
                  <div class="form-check ms-3">
                    <input class="form-check-input" type="checkbox" name="column-score-${key}" ${option.count_score ? 'checked' : ''} />
                    <label class="form-check-label">Count Score</label>
                  </div>
                  <button type="button" class="btn btn-danger ms-2" onclick="removeColumnField(this)"><i class="fas fa-trash"></i></button>
                </div>
              </div>`;
    });
  }
  
  html += `
            </div>
            <button type="button" class="btn btn-success" onclick="addColumnField()">Add Column</button>
          </div>
          
          <div class="table-responsive">
            <table class="table table-bordered" id="edit-checklist-table">
              <thead>
                <tr>
                  <th style="width: 100px;">Item #</th>
                  <th>Question Text</th>
                  <th style="width: 100px;">Essential</th>
                </tr>
              </thead>
              <tbody>`;

  // Add items - preserve original keys and handle subtitles
  if (content.questions?.details?.items) {
    Object.entries(content.questions.details.items).forEach(([key, item]) => {
      const subtitleClass = item.subtitle ? `subtitle-${item.subtitle}` : '';
      const indentStyle = item.subtitle === 3 ? 'style="padding-left: 40px;"' :
                         item.subtitle === 2 ? 'style="padding-left: 20px;"' :
                         item.subtitle === 1 ? 'style="padding-left: 10px;"' : '';

      html += `
                <tr class="${subtitleClass}">
                  <td>${key}</td>
                  <td ${indentStyle}>
                    <textarea class="form-control" name="item-text-${key}" rows="3">${item.text || ''}</textarea>
                    <div class="mt-2">
                      <label class="form-label">Subtitle Level:</label>
                      <select class="form-control form-select" name="item-subtitle-${key}">
                        <option value="" ${item.subtitle === undefined ? 'selected' : ''}>None</option>
                        <option value="1" ${item.subtitle === 1 ? 'selected' : ''}>Level 1</option>
                        <option value="2" ${item.subtitle === 2 ? 'selected' : ''}>Level 2</option>
                        <option value="3" ${item.subtitle === 3 ? 'selected' : ''}>Level 3</option>
                      </select>
                    </div>
                  </td>
                  <td class="text-center">
                    <input class="form-check-input" type="checkbox" name="item-essential-${key}" ${item.essential ? 'checked' : ''} />
                  </td>
                </tr>`;
    });
  }
  
  html += `
              </tbody>
            </table>
          </div>
        </div>
      </div>`;
  
  // Continue with additional entry section...
  html += generateAdditionalEntrySection(content);
  html += `</form>`;

  $('#edit-test-content').html(html);
}

function generateAdditionalEntrySection(content) {
  let html = `
      <div class="card mt-3">
        <div class="card-header">
          <input class="form-control" type="text" name="additional-entry-header" value="${content.additional_entry?.header || ''}" placeholder="Additional Entry Header" />
        </div>
        <div class="card-body">
          <div id="additional-entry-fields">`;

  // Add existing additional entry fields
  if (content.additional_entry?.details) {
    Object.entries(content.additional_entry.details).forEach(([key, field]) => {
      html += `
            <div class="mb-3 entry-field" data-entry-key="${key}">
              <div class="input-group">
                <span class="input-group-text">Title:</span>
                <input class="form-control" type="text" name="additional-entry-title-${key}" value="${field.title || ''}" placeholder="Field Title" />
                <span class="input-group-text">Type:</span>
                <select class="form-control form-select" name="additional-entry-type-${key}">
                  <option value="text" ${field.input === 'text' ? 'selected' : ''}>Text</option>
                  <option value="date" ${field.input === 'date' ? 'selected' : ''}>Date</option>
                  <option value="test_date" ${field.input === 'test_date' ? 'selected' : ''}>Test Date</option>
                  <option value="rank" ${field.input === 'rank' ? 'selected' : ''}>Rank</option>
                  <option value="department_and_unit" ${field.input === 'department_and_unit' ? 'selected' : ''}>Department & Unit</option>
                  <option value="custom_options" ${Array.isArray(field.input) ? 'selected' : ''}>Custom Options</option>
                </select>
                <button type="button" class="btn btn-danger ms-2" onclick="removeEntryField(this)"><i class="fas fa-trash"></i></button>
              </div>
              ${Array.isArray(field.input) ? `
              <div class="mt-2">
                <input class="form-control" type="text" name="additional-entry-options-${key}" value="${field.input.join('|')}" placeholder="Options separated by |" />
              </div>` : ''}
              <div class="form-check mt-2">
                <input class="form-check-input" type="checkbox" name="additional-entry-required-${key}" ${field.required ? 'checked' : ''} />
                <label class="form-check-label">Required</label>
              </div>
            </div>`;
    });
  }

  html += `
          </div>
          <button type="button" class="btn btn-success" onclick="addEntryField('additional')">Add Additional Entry Field</button>
        </div>
      </div>`;

  return html;
}

function generateMCEditForm(data) {
  const content = data.content;
  const info = data.info;

  let html = `
    <form id="edit-mc-form">
      <input type="hidden" name="test_name" value="${info.test_name}" />
      <input type="hidden" name="test_version" value="${info.version}" />

      <div class="card mt-3">
        <div class="card-header">
          <h3>Test Configuration</h3>
        </div>
        <div class="card-body">
          <div class="row">
            <div class="col-md-6">
              <label class="form-label">Version:</label>
              <input class="form-control" type="text" value="${info.version}" disabled />
            </div>
            <div class="col-md-6">
              <label class="form-label">Version Name:</label>
              <input class="form-control" type="text" name="version-name" value="${info.version_name || ''}" />
            </div>
          </div>
          <div class="row mt-3">
            <div class="col-md-4">
              <label class="form-label">Score per Question:</label>
              <input class="form-control" type="number" name="score-per-q" value="${info.score_per_question || ''}" />
            </div>
            <div class="col-md-4">
              <label class="form-label">Total Score:</label>
              <input class="form-control" type="number" name="total-score" value="${info.total_score || ''}" />
            </div>
            <div class="col-md-4">
              <label class="form-label">Pass Score:</label>
              <input class="form-control" type="number" name="pass-score" value="${info.pass_score || ''}" />
            </div>
          </div>
        </div>
      </div>

      <div class="card mt-3">
        <div class="card-header">
          <input class="form-control" type="text" name="intro-header" value="${content.intro?.header || ''}" placeholder="Test Name Here" />
        </div>
        <div class="card-body">
          <textarea class="form-control" name="intro-details" rows="5" placeholder="Introduction / Instruction Here">${content.intro?.details || ''}</textarea>
        </div>
      </div>

      <div class="card mt-3">
        <div class="card-header">
          <input class="form-control" type="text" name="questions-header" value="${content.questions?.header || ''}" placeholder="Questions Header" />
        </div>
        <div class="card-body">`;

  // Add questions
  if (content.questions?.details) {
    Object.entries(content.questions.details).forEach(([key, question]) => {
      html += `
          <div class="card mb-3">
            <div class="card-header">
              <h5>Question ${key}</h5>
            </div>
            <div class="card-body">
              <div class="mb-3">
                <label class="form-label">Question:</label>
                <textarea class="form-control" name="${key}" rows="3">${question.q || ''}</textarea>
              </div>
              <div class="mb-3">
                <label class="form-label">Correct Answer:</label>
                <input class="form-control" type="text" name="${key}-ans" value="${question.ans || ''}" />
              </div>
              <div class="mb-3">
                <label class="form-label">Options:</label>`;

      if (question.opt && Array.isArray(question.opt)) {
        question.opt.forEach((option, index) => {
          html += `
                <input class="form-control mb-2" type="text" name="${key}_${index + 1}" value="${option}" placeholder="Option ${index + 1}" />`;
        });
      }

      html += `
              </div>
            </div>
          </div>`;
    });
  }

  html += `
        </div>
      </div>
    </form>
  `;

  $('#edit-test-content').html(html);
}

// Helper functions for managing entry fields and columns
function addEntryField(type) {
  const container = document.getElementById(`${type}-entry-fields`);
  const fieldCount = container.querySelectorAll('.entry-field').length + 1;
  const key = `${type}_entry_${fieldCount.toString().padStart(2, '0')}`;

  const fieldHtml = `
    <div class="mb-3 entry-field" data-entry-key="${key}">
      <div class="input-group">
        <span class="input-group-text">Title:</span>
        <input class="form-control" type="text" name="${type}-entry-title-${key}" placeholder="Field Title" />
        <span class="input-group-text">Type:</span>
        <select class="form-control form-select" name="${type}-entry-type-${key}">
          <option value="text">Text</option>
          <option value="date">Date</option>
          <option value="test_date">Test Date</option>
          <option value="rank">Rank</option>
          <option value="department_and_unit">Department & Unit</option>
          <option value="custom_options">Custom Options</option>
        </select>
        <button type="button" class="btn btn-danger ms-2" onclick="removeEntryField(this)"><i class="fas fa-trash"></i></button>
      </div>
      <div class="form-check mt-2">
        <input class="form-check-input" type="checkbox" name="${type}-entry-required-${key}" />
        <label class="form-check-label">Required</label>
      </div>
    </div>`;

  container.insertAdjacentHTML('beforeend', fieldHtml);
}

function removeEntryField(button) {
  const fieldDiv = button.closest('.entry-field');
  fieldDiv.remove();
}

function addColumnField() {
  const container = document.getElementById('column-config');
  const fieldCount = container.querySelectorAll('.column-field').length + 1;
  const key = `o${fieldCount.toString().padStart(2, '0')}`;

  const fieldHtml = `
    <div class="mb-3 column-field" data-column-key="${key}">
      <div class="input-group">
        <span class="input-group-text">Column Name:</span>
        <input class="form-control" type="text" name="column-name-${key}" placeholder="Column Name" />
        <span class="input-group-text">Type:</span>
        <select class="form-control form-select" name="column-type-${key}">
          <option value="checkbox">Checkbox</option>
          <option value="text">Text</option>
        </select>
        <div class="form-check ms-3">
          <input class="form-check-input" type="checkbox" name="column-score-${key}" />
          <label class="form-check-label">Count Score</label>
        </div>
        <button type="button" class="btn btn-danger ms-2" onclick="removeColumnField(this)"><i class="fas fa-trash"></i></button>
      </div>
    </div>`;

  container.insertAdjacentHTML('beforeend', fieldHtml);
}

function removeColumnField(button) {
  const fieldDiv = button.closest('.column-field');
  fieldDiv.remove();
}

function saveTestChanges() {
  const testType = $('#edit-test-type').val();
  const testName = $('#edit-test-name').val();
  const testVersion = $('#edit-test-version').val();

  let formData;
  if (testType === 'checklist') {
    formData = new FormData(document.getElementById('edit-checklist-form'));
  } else if (testType === 'mc') {
    formData = new FormData(document.getElementById('edit-mc-form'));
  }

  // Convert FormData to object
  const data = {};
  for (let [key, value] of formData.entries()) {
    data[key] = value;
  }

  // Show confirmation modal instead of alert
  showSaveConfirmationModal(testType, testName, testVersion, data);
}

function showSaveConfirmationModal(testType, testName, testVersion, data) {
  const modalHtml = `
    <div class="modal fade" id="saveConfirmModal" tabindex="-1" aria-labelledby="saveConfirmModalLabel" aria-hidden="true">
      <div class="modal-dialog">
        <div class="modal-content">
          <div class="modal-header">
            <h5 class="modal-title" id="saveConfirmModalLabel">Confirm Save Changes</h5>
            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
          </div>
          <div class="modal-body">
            <p>Are you sure you want to save changes to:</p>
            <ul>
              <li><strong>Test:</strong> ${testName.replace(/_/g, ' ')}</li>
              <li><strong>Version:</strong> ${testVersion}</li>
              <li><strong>Type:</strong> ${testType.toUpperCase()}</li>
            </ul>
            <p class="text-warning"><strong>Warning:</strong> This will overwrite the existing test data.</p>
          </div>
          <div class="modal-footer">
            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
            <button type="button" class="btn btn-success" id="confirmSaveBtn">Save Changes</button>
          </div>
        </div>
      </div>
    </div>`;

  // Remove existing modal if any
  $('#saveConfirmModal').remove();

  // Add modal to body
  $('body').append(modalHtml);

  // Show modal
  $('#saveConfirmModal').modal('show');

  // Handle confirm button click
  $('#confirmSaveBtn').off('click').on('click', function() {
    $('#saveConfirmModal').modal('hide');
    performSaveRequest(testType, testName, testVersion, data);
  });
}

function performSaveRequest(testType, testName, testVersion, data) {
  // Send update request
  $.ajax({
    url: '/api/update-test',
    method: 'POST',
    contentType: 'application/json',
    data: JSON.stringify({
      test_name: testName,
      version: testVersion,
      test_type: testType,
      form_data: data
    }),
    success: function(response) {
      if (response.success) {
        showSuccessModal();
      } else {
        showErrorModal('Error updating test: ' + (response.error || 'Unknown error'));
      }
    },
    error: function() {
      showErrorModal('Error updating test. Please try again.');
    }
  });
}

function showSuccessModal() {
  const modalHtml = `
    <div class="modal fade" id="successModal" tabindex="-1" aria-labelledby="successModalLabel" aria-hidden="true">
      <div class="modal-dialog">
        <div class="modal-content">
          <div class="modal-header bg-success text-white">
            <h5 class="modal-title" id="successModalLabel">Success</h5>
            <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
          </div>
          <div class="modal-body">
            <p><i class="fas fa-check-circle text-success"></i> Test updated successfully!</p>
          </div>
          <div class="modal-footer">
            <button type="button" class="btn btn-success" onclick="location.reload()">Refresh Page</button>
          </div>
        </div>
      </div>
    </div>`;

  $('#successModal').remove();
  $('body').append(modalHtml);
  $('#editTestFullModal').modal('hide');
  $('#successModal').modal('show');
}

function showErrorModal(message) {
  const modalHtml = `
    <div class="modal fade" id="errorModal" tabindex="-1" aria-labelledby="errorModalLabel" aria-hidden="true">
      <div class="modal-dialog">
        <div class="modal-content">
          <div class="modal-header bg-danger text-white">
            <h5 class="modal-title" id="errorModalLabel">Error</h5>
            <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
          </div>
          <div class="modal-body">
            <p><i class="fas fa-exclamation-triangle text-danger"></i> ${message}</p>
          </div>
          <div class="modal-footer">
            <button type="button" class="btn btn-danger" data-bs-dismiss="modal">Close</button>
          </div>
        </div>
      </div>
    </div>`;

  $('#errorModal').remove();
  $('body').append(modalHtml);
  $('#errorModal').modal('show');
}
