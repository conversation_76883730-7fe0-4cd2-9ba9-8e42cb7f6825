#%%

import pyrebase

from config import *


firebase = pyrebase.initialize_app(FIREBASE_CONFIG)

db = firebase.database()
auth = firebase.auth()

#%%
# Push Data
""" data = {
    'Department': 'ORT',
    'Ward': '3D',
    'Last Name': 'LAM',
    'First Name': '<PERSON>u Ming',
    'CORP ID': '468437'
}

 """


# permission = admin_permission + unit_permission


# db.child('permission').set(permission)


# Update Data

# db.child(data['CORP ID']).update({'Ward':'2A','Department':'MED'})


# Delete
# db.child(data['CORP ID']).remove()

#%%

# Get Data

# db_permission = db.get('permission')

# %%

""" for unit in unit_permission:
    auth.create_user_with_email_and_password(
        email=unit['unit'],
        password=unit['unit'].split('.')[0]
    ) """

# %%

tests = dict(db.child('test').get().val())

for test_name, test_versions in tests.items():
    if test_name.startswith('PCA'):
        for version_name, test_version in test_versions.items():
            if test_version['info'].get('form_type','') == 'checklist':
                if 'basic_entry' in test_version['content']:
                    # print(f"header: {test_version['content']['additional_entry']['header']}")
                    # if '評分結果' in test_version['content']['additional_entry']['header']:
                    #     print(f'Handling {test_name} {version_name}...')
                    #     tests[test_name][version_name]['content']['additional_entry']['header'] = '評語'

                    # tests[test_name][version_name]['content']['basic_entry']['details'] = {
                    #     k:v
                    #     for k,v in test_version['content']['additional_entry']['details'].items()
                    #     if '總評分等級' not in v['title']
                    # }

                    print(f"Handling {test_name} {version_name}...")
                    for k,v in test_version['content']['basic_entry']['details'].items():
                        if k == 'basic_entry_02' and v['title'] == '職級':
                            print(f'Changing to 指導員職級...')
                            tests[test_name][version_name]['content']['basic_entry']['details'][k]['title'] = '指導員職級'
                        elif k == 'basic_entry_05' and v['title'] == '職級':
                            print(f'Changing to 觀察員職級...')
                            tests[test_name][version_name]['content']['basic_entry']['details'][k]['title'] = '觀察員職級'
        


db.child('test').set(tests)
                
# %%

def change_dept_of_unit(db, unit, old_dept, new_dept):

    permission = dict(db.child('permission').get().val())
    for key, data in permission.items():
        if key == unit:
            permission[key]['dept'] = new_dept
            permission[key]['access'] = [new_dept]

    db.child('permission').set(permission)
    print(f'Changed department of {unit} from {old_dept} to {new_dept}')

    for discipline in ['nurse', 'pca']:
        staff = dict(db.child(discipline).get().val())
        found = False
        for employee_id, data in staff.items():
            if data['Unit'] == unit:
                staff[employee_id]['Department'] = new_dept
                found = True
        
        if found:
            db.child(discipline).set(staff)
            print(f'Changed {discipline} of {unit} from {old_dept} to {new_dept}')
# %%

def merge_units(db, unit_a, unit_b):
    '''
    unit_b will be merged into unit_a
    '''

    permission = dict(db.child('permission').get().val())
    found_permission = False
    for key, data in permission.items():
        if key == unit_b:
            found_permission = True
            break

    if found_permission:
        removed_permission = permission.pop(unit_b)
        db.child('permission').set(permission)
        print(f'Removed {unit_b} from permission')

    else:
        print(f'Unit {unit_b} not found in permission')

    for discipline in ['nurse', 'pca']:
        staff = dict(db.child(discipline).get().val())
        found = False
        for employee_id, data in staff.items():
            if data['Unit'] == unit_b:
                staff[employee_id]['Unit'] = unit_a
                staff[employee_id]['Department'] = permission[unit_a]['dept']
                found = True
        
        if found:
            db.child(discipline).set(staff)
            print(f'Merged {unit_b} into {unit_a}')

        else:
            print(f'Unit {unit_b} not found in {discipline}')

#%%

def change_unit_name(db, old_name, new_name):

    permission = dict(db.child('permission').get().val())

    found_permission = False
    for key, data in permission.items():
        if key == old_name:
            found_permission = True
            new_permission = data
            new_permission['unit'] = new_name
            permission[new_name] = new_permission
            break
    
    if found_permission:
        removed_permission = permission.pop(old_name)
        db.child('permission').set(permission)
        print(f'Changed unit name from {old_name} to {new_name}')

    else:
        print(f'Unit {old_name} not found in permission')

    for discipline in ['nurse', 'pca']:
        staff = dict(db.child(discipline).get().val())
        found = False
        for employee_id, data in staff.items():
            if data['Unit'] == old_name:
                found = True
                staff[employee_id]['Unit'] = new_name

        if found:
            db.child(discipline).set(staff)
            print(f'Changed unit name of {discipline} from {old_name} to {new_name}')

        else:
            print(f'Unit {old_name} not found in {discipline}')
# %%

def change_staff_unit(db, employee_id, new_unit):

    staff_discipline = ''
    for discipline in ['nurse', 'pca', 'temp_staff']:
        staff = dict(db.child(discipline).get().val()) if db.child(discipline).get().val() else {}
        if staff:
            staff_discipline = discipline
            break

        
    if not staff: 
        print('No staff found')
        return

    updated = False
    for key, data in staff.items():
        if key == employee_id:
            permissions = dict(db.child('permission').get().val()) if db.child('permission').get().val() else {}
            if permissions:
                dept = permissions.get(new_unit, {}).get('dept', '')
                if dept:
                    staff[key]['Department'] = dept
                    staff[key]['Unit'] = new_unit
                    updated = True
                else:
                    print(f'No department found for {new_unit}')
                    return
            break
                
    if updated:
        db.child(staff_discipline).set(staff)
        print(f'Changed unit of {employee_id} from {staff_discipline} to {new_unit}')
# %%

# %%
def change_test_name(db, old_test_name, new_test_name):
    old_tests = dict(db.child('test').child(old_test_name).get().val())
    new_tests = {}
    for v_id, version in old_tests.items():
        temp_test = version
        temp_test['info']['test_name'] = new_test_name
        new_tests[v_id] = temp_test

    db.child('test').child(new_test_name).set(new_tests)
    db.child('test').child(old_test_name).remove()

#%%

tests = dict(db.child('test').get().val())
for test_name, data in tests.items():
    for v_id, version in data.items():
        if version['info']['form_type'] == 'checklist':
            for o, option in version['content']['questions']['details']['options'].items():
                if 'count_score' not in option:
                    print(f'Adding count_score for {test_name} {v_id} {o}')
                    tests[test_name][v_id]['content']['questions']['details']['options'][o]['count_score'] = False
                    if option['name'] == '滿意':
                        tests[test_name][v_id]['content']['questions']['details']['options'][o]['count_score'] = True

db.child('test').set(tests)
    
# %%

tests = dict(db.child('test').get().val())
for test_name, data in tests.items():
    print(f'Handling {test_name}')
    for v_id, version in data.items():
        if version['info']['form_type'] == 'checklist':
            for entry_type in['basic_entry', 'additional_entry']:
                if entry_type in version['content']:
                    for b_k, b_v in version['content'][entry_type]['details'].items():
                        if b_v['title'] == '指導員姓名' or b_v['title'] == '觀察員姓名':
                            try:
                                next_b_k = list(version['content'][entry_type]['details'].keys())[list(version['content'][entry_type]['details'].values()).index(b_k)+1]
                            except:
                                next_b_k = None
                            if next_b_k == '職級':
                                print(f'Changing {test_name} {v_id} {entry_type} {b_k} {b_v["title"]} title')
                                tests[test_name][v_id]['content'][entry_type]['details'][next_b_k]['title'] = f'{b_v["title"].strip().replace("姓名", "")}職級'
                                
db.child('test').set(tests)
# %%

from datetime import datetime
import copy

# Delete old records

pcas = dict(db.child('pca').get().val())
new_pcas = {}
for employee_id, data in pcas.items():
    new_pcas[employee_id] = copy.deepcopy(data)
    if 'Assessments' in data:
        for test_name, test_versions in data['Assessments'].items():
            if "Fall" not in test_name and "AED" not in test_name:
                for ver_id, record in test_versions.items():
                    try:
                        test_time = datetime.strptime(record['time'],'%Y-%m-%d %H:%M')
                    except:
                        test_time = datetime.strptime(record['time'],'%Y-%m-%d')
                    if test_time.year < 2025:
                        print(f'Deleting {test_name} {ver_id} for {employee_id} (Test done on {test_time})')
                        new_pcas[employee_id]['Assessments'][test_name].pop(ver_id)
                        if new_pcas[employee_id]['Assessments'][test_name] == {}:
                            new_pcas[employee_id]['Assessments'].pop(test_name)

db.child('pca').set(new_pcas)
# %%

tests = dict(db.child('test').get().val())
temp_dict_1 = tests['PCA_019']['v001']['content']['basic_entry']['details'].pop('basic_entry_03')
tests['PCA_019']['v001']['content']['basic_entry']['details']['basic_entry_03'] = tests['PCA_019']['v001']['content']['basic_entry']['details']['basic_entry_02']
tests['PCA_019']['v001']['content']['basic_entry']['details']['basic_entry_02'] = temp_dict_1

db.child('test').set(tests)


# %%
