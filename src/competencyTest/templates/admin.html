{% extends 'base.html' %} 

{% block content %}

<style>

.modal-header-color {
  background-color: #f3fb64;
}

</style>

<div class="container mt-5">
  <h1 class="mb-5">Admin</h1>
  <div class="row">
    <div class="col-lg-4 col-md-6 mb-4">
      <div class="card h-100">
        <div class="card-body">
          <h5 class="card-title">Delete</h5>
          <p class="card-text">Delete Operator</p>
        </div>
        <div class="card-footer">
          <a href="{{ url_for('delete_operator') }}" class="btn btn-danger"
            >Go to Delete</a
          >
        </div>
      </div>
    </div>
    <div class="col-lg-4 col-md-6 mb-4">
      <div class="card h-100">
        <div class="card-body">
          <h5 class="card-title">Add/Edit Units</h5>
          <p class="card-text">Change access right, or Add new Units</p>
        </div>
        <div class="card-footer">
          <a href="{{ url_for('edit_unit') }}" class="btn btn-success"
            >Go to Add/Edit</a
          >
        </div>
      </div>
    </div>
    <div class="col-lg-4 col-md-6 mb-4">
      <div class="card h-100">
        <div class="card-body">
          <h5 class="card-title">New Test</h5>
          <p class="card-text">Create new test</p>
        </div>
        <div class="card-footer">
          <a class="btn btn-warning"
            data-bs-toggle="modal"
            data-bs-target="#newTestModal"
            >Create</a
          >
        </div>
      </div>
    </div>
    <div class="col-lg-4 col-md-6 mb-4">
      <div class="card h-100">
        <div class="card-body">
          <h5 class="card-title">Edit Test</h5>
          <p class="card-text">Allow minor edits on current version</p>
        </div>
        <div class="card-footer">
          <a class="btn btn-primary"
            data-bs-toggle="modal"
            data-bs-target="#editTestModal"
            >Choose Test</a
          >
        </div>
      </div>
    </div>
  </div>
</div>

<div 
  class="modal fade" 
  id="newTestModal" 
  tabindex="-1" 
  aria-labelledby="newTestModalLabel" 
  aria-hidden="true"
>
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header modal-header-color">
        <h1 class="modal-title fs-5" id="newTestModalLabel">New Test</h1>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body">
        <form action="" id="newTestForm" method="POST">
          <input type="hidden" name="new_test" value="new_test" />
          <div class="mb-3">
            <label for="test-type" class="form-label">Test Type</label>
            <select 
              id="test-type"
              name="test-type"
              class="form-select form-select-lg mb-3" 
              aria-label="test-type"
            >
              <option value="checklist">Checklist</option>
              <option value="mc">MC Quiz</option>
            </select>
          </div>

          <div class="rounded border border-2 p-2 mt-2 mb-2">
            <div class="mb-2">
              <label for="test-name" class="form-label">Test Name</label>
              <select 
                id="test-name"
                name="test-name"
                class="form-select form-select-lg mb-3" 
                aria-label="test-name"
              >
                <option value="new">[ NEW ]</option>
                {% for test,info in tests_overview.items() %}
                <option value="{{test}}">{{test|replace('_',' ')}}</option>
                {% endfor %}
              </select>
            </div>
            <div class="mb-3 test-version-input">
              <label for="test-version" class="form-label">Based on Version:</label>
              <select 
                id="test-version"
                name="test-version"
                class="form-select form-select-lg mb-3" 
                aria-label="test-version"
              >
                <!-- Options generated in function versionHandler -->
              </select>
            </div>
            <div class="mb-3 new-test-name-input">
              <label for="new-test-name" class="form-label">New Test Name</label>
              <input
                type="text"
                id="new-test-name"
                name="new-test-name"
                class="form-control" 
              />
            </div>
          </div>
          
        </form> 
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
        <button type="button" class="btn btn-primary" onclick="postRequest()">Create</button>
      </div>
    </div>
  </div>
</div>

<!-- Edit Test Modal -->
<div
  class="modal fade"
  id="editTestModal"
  tabindex="-1"
  aria-labelledby="editTestModalLabel"
  aria-hidden="true"
>
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header modal-header-color">
        <h1 class="modal-title fs-5" id="editTestModalLabel">Edit Test</h1>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body">
        <div class="mb-3">
          <label for="edit-test-type" class="form-label">Test Type</label>
          <select
            id="edit-test-type"
            name="edit-test-type"
            class="form-select form-select-lg mb-3"
            aria-label="edit-test-type"
          >
            <option value="">-- Choose Test Type --</option>
            <option value="checklist">Checklist</option>
            <option value="mc">MC Quiz</option>
          </select>
        </div>

        <div class="mb-3">
          <label for="edit-test-name" class="form-label">Test Name</label>
          <select
            id="edit-test-name"
            name="edit-test-name"
            class="form-select form-select-lg mb-3"
            aria-label="edit-test-name"
            disabled
          >
            <option value="">-- Choose Test --</option>
          </select>
        </div>

        <div class="mb-3">
          <label for="edit-test-version" class="form-label">Version</label>
          <select
            id="edit-test-version"
            name="edit-test-version"
            class="form-select form-select-lg mb-3"
            aria-label="edit-test-version"
            disabled
          >
            <option value="">-- Choose Version --</option>
          </select>
        </div>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
        <button type="button" class="btn btn-primary" id="edit-test-confirm" disabled>Edit Test</button>
      </div>
    </div>
  </div>
</div>

<!-- Edit Test Full Screen Modal -->
<div
  class="modal fade"
  id="editTestFullModal"
  tabindex="-1"
  aria-labelledby="editTestFullModalLabel"
  aria-hidden="true"
>
  <div class="modal-dialog modal-fullscreen">
    <div class="modal-content">
      <div class="modal-header" style="background-color: #26a682">
        <h5 class="modal-title" id="editTestFullModalLabel" style="color: white">Edit Test</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body" id="edit-test-content">
        <!-- Content will be dynamically injected here -->
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
        <button type="button" class="btn btn-success" id="save-test-changes">Save Changes</button>
      </div>
    </div>
  </div>
</div>


<script src="{{ url_for('static', filename='js/admin_edit.js') }}"></script>

<script>

function toggleTestVersionInput() {

    const selectedValue = $('#test-name').val();

    if (selectedValue === 'new') {
      $('.test-version-input').hide();
      $('.new-test-name-input').show();
    } else {
      $('.test-version-input').show();
      $('.new-test-name-input').hide();
    }

}

function updateTestVersion() {

  const selectedValue = $('#test-name').val();
  const testsOverview = {{ tests_overview | tojson }};
  const selectedTestVersions = Object.entries(testsOverview[selectedValue] || {});

  // Clear existing options
  $('#test-version').empty();

  // Generate and append new options
  selectedTestVersions.forEach(([ver,info]) => {
    
    const option = $('<option>')
      .val(ver)
      .text(`${info.version} - ${info.version_name}`);
    $('#test-version').append(option);

  } )

}

function testName__onChange() {

  toggleTestVersionInput();

  updateTestVersion();

}

function postRequest() {

  const form = $('#newTestForm');
  form.submit();

}

// Set global variable for tests overview
window.testsOverview = {{ tests_overview | tojson }};









$(document).ready(function() {

  $('#test-name').on('change', testName__onChange);
  $('#test-name').trigger('change');

  // Edit Test Modal Event Handlers
  $('#edit-test-type').on('change', editTestType__onChange);
  $('#edit-test-name').on('change', editTestName__onChange);
  $('#edit-test-version').on('change', editTestVersion__onChange);
  $('#edit-test-confirm').on('click', editTestConfirm);
  $('#save-test-changes').on('click', saveTestChanges);

});


</script>


{% endblock %}
